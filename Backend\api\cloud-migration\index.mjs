/**
 * @fileoverview Cloud Migration Cost Calculator form submission handler
 * This AWS Lambda function handles cloud migration cost calculator form submissions by:
 * 1. Parsing cloud migration form data from the event
 * 2. Sending data to HubSpot CRM with migration requirements and cost estimates
 * 3. Sending detailed cost calculation report email via SendGrid
 * 4. Sending notification to Slack with migration summary
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import sendDataToHubspot from "../../common/sendDataToHubSpot.mjs";
import sendDataToSendGrid from "../../common/sendDataToSendGrid.mjs";
import currentTimestamp from "../../common/currentTimestamp.mjs";
import sendToSlack from "../../common/sendDataToSlack.mjs";
import { getConfigValue } from "../../common/ssmConfig.mjs";

/**
 * AWS Lambda handler for cloud migration cost calculator form submissions
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing cloud migration form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Example event.body structure (includes all contact fields plus migration-specific fields):
 * {
 *   "firstName": "John",
 *   "lastName": "Smith",
 *   "emailAddress": "<EMAIL>",
 *   "companyName": "Tech Solutions Inc",
 *   "phoneNumber": "+1234567890",
 *   "currentInfrastructure": "On-premises",
 *   "workloadType": "Web Applications",
 *   "dataVolume": "1-10 TB",
 *   "complianceRequirements": "GDPR, SOC2",
 *   "migrationTimeline": "3-6 months",
 *   "budgetRange": "$50,000 - $100,000",
 *   "primaryGoals": "Cost reduction, Scalability",
 *   "currentChallenges": "High maintenance costs, Limited scalability"
 * }
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"hubspotResponse\":\"CloudMigration form data sent to HubSpot successfully.\"}"
 * }
 */
export const handler = async (event) => {
  try {
    const form_data = JSON.parse(event.body);

    console.log(currentTimestamp());
    console.log("Cloud Migration Form Data Received:", form_data);

    // Map form fields to HubSpot properties
    const formFields = [
      { name: "firstname", value: form_data?.firstName ?? "" },
      { name: "lastname", value: form_data?.lastName ?? "" },
      { name: "email", value: form_data?.emailAddress ?? "" },
      { name: "phone", value: form_data?.phoneNumber ?? "" },
      { name: "company", value: form_data?.companyName ?? "" },
      { name: "current_infrastructure", value: form_data?.currentInfrastructure ?? "" },
      { name: "workload_type", value: form_data?.workloadType ?? "" },
      { name: "data_volume", value: form_data?.dataVolume ?? "" },
      { name: "compliance_requirements", value: form_data?.complianceRequirements ?? "" },
      { name: "migration_timeline", value: form_data?.migrationTimeline ?? "" },
      { name: "budget_range", value: form_data?.budgetRange ?? "" },
      { name: "primary_goals", value: form_data?.primaryGoals ?? "" },
      { name: "current_challenges", value: form_data?.currentChallenges ?? "" },
      { name: "how_did_you_hear_about_us_", value: form_data?.howDidYouHearAboutUs ?? "" },
      { name: "message", value: form_data?.additionalComments ?? "" },
      { name: "consent", value: form_data?.consent ?? "" },
    ];

    // Get configuration values from SSM
    const [hubspotApiKey, hubspotFormGuid] = await Promise.all([
      getConfigValue("HUBSPOT_API_KEY"),
      getConfigValue("HUBSPOT_CLOUD_MIGRATION_FORM_GUID"),
    ]);

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${hubspotApiKey}`,
      },
    };

    try {
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        hubspotFormGuid
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Get email configuration from SSM
        const [mailTo, mailFrom, emailTemplateId] = await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID"),
        ]);

        const emailRes = await sendDataToSendGrid(
          mailTo,
          mailFrom,
          form_data?.emailAddress,
          emailTemplateId,
          form_data
        );

        // Send Data to success Slack channel (webhook URL will be determined by sendToSlack)
        await sendToSlack(form_data);

        console.log(currentTimestamp());
        console.log("Lead Data", form_data);
        console.log("HubSpot Response", hubspotResponse);
        console.log("SendGrid Email Response", emailRes);

        return {
          statusCode: 200,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "POST,OPTIONS"
          },
          body: JSON.stringify({
            message: "Form submitted successfully.",
            hubspotResponse: hubspotResponse.message,
          }),
        };
      } else {
        console.error("HubSpot Error:", hubspotResponse);

        let formLeadData = form_data;
        formLeadData.page_name = form_data?.secondary_source;
        formLeadData.failed_source = "Hubspot";

        // Get failure email configuration from SSM
        const [failureMailTo, failureMailFrom, failureTemplateId] =
          await Promise.all([
            getConfigValue("MAIL_TO"),
            getConfigValue("MAIL_FROM"),
            getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
          ]);

        const failureEmail = await sendDataToSendGrid(
          failureMailTo,
          failureMailFrom,
          form_data?.emailAddress,
          failureTemplateId,
          formLeadData
        );

        // Send to failure Slack channel (webhook URL will be determined by sendToSlack)
        await sendToSlack(
          form_data,
          null, // Let sendToSlack determine the webhook URL
          "⚠️ HubSpot Form Submission Failed ⚠️"
        );

        console.log("Failure Email Response", failureEmail);

        return {
          statusCode: 500,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
            "Access-Control-Allow-Methods": "POST,OPTIONS"
          },
          body: JSON.stringify({
            message: "Error while submitting form to HubSpot.",
            error: hubspotResponse.error,
          }),
        };
      }
    } catch (hubspotError) {
      console.error("HubSpot submission error:", hubspotError);

      let formLeadData = form_data;
      formLeadData.page_name = form_data?.secondary_source;
      formLeadData.failed_source = "Hubspot";

      // Get failure email configuration from SSM
      const [failureMailTo, failureMailFrom, failureTemplateId] =
        await Promise.all([
          getConfigValue("MAIL_TO"),
          getConfigValue("MAIL_FROM"),
          getConfigValue("SENDGRID_FAILURE_EMAIL_TEMPLATE_ID"),
        ]);

      const failureEmail = await sendDataToSendGrid(
        failureMailTo,
        failureMailFrom,
        form_data?.emailAddress,
        failureTemplateId,
        formLeadData
      );

      // Send to failure Slack channel
      await sendToSlack(
        form_data,
        null,
        "⚠️ HubSpot Form Submission Failed ⚠️"
      );

      console.log("Failure Email Response", failureEmail);

      return {
        statusCode: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS"
        },
        body: JSON.stringify({
          message: "Error while submitting form to HubSpot.",
          error: "HubSpot submission failed",
        }),
      };
    }
  } catch (error) {
    console.error("Cloud Migration form handler error:", error);

    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
        "Access-Control-Allow-Methods": "POST,OPTIONS"
      },
      body: JSON.stringify({
        message: "Internal server error",
        error: error.message,
      }),
    };
  }
};
