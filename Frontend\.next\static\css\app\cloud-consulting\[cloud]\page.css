/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/variables.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Colors */
/* End Colors */

/* SPACING */
/* END SPACING */

/* FONT SIZES */
/* END FONT SIZES */

/*FONT WEIGHTS */
/* END FONT WEIGHTS */
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Button/Button.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.Button_button__exqP_,
.Button_link__9n7Et {
  position: relative;
  background-color: #000000;
  background-image: linear-gradient(#000000, #000000),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  font-size: 14px;
  font-weight: 600;
  padding: 8px 14px;
  cursor: pointer;
  line-height: 21px;
  letter-spacing: 0.2px;
  color: #FFFFFF;
  transition: 0.2s linear;
  min-width: 120px;
  min-height: 40px;
  border: 2px solid transparent;
  /* Required to create the border effect */
  border-radius: 3px;
}


.Button_button__exqP_:hover {

  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.Button_innerWrapper__ITLB1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.Button_leftWrapper__fWtI9 {
  margin-right: 8px;
}

.Button_rightWrapper__GkIh_ {
  margin-left: 8px;
}

.Button_link__9n7Et {
  display: inline-block;
  color: #FFFFFF;
  text-decoration: none;
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/breakpoints.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/


/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/AuditMethodology/AuditMethodology.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
.AuditMethodology_container__GpITt {
  background-color: #000000;
  color: #FFFFFF;

  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 40px;
  padding: 80px 0;

  background-image: url('https://cdn.marutitech.com/Group_5044_662af9d22b.svg'),
    url('https://cdn.marutitech.com/Group_5043_745de9790c.svg');
  background-repeat: no-repeat, no-repeat;
  background-position:
    52% 3%,
    calc(50% + 560px) 100%;
  background-size: 300px 240px;

  @media screen and (max-width: 1200px) {
    gap: 30px;
    padding: 40px 32px;

    background-position:
      calc(52% - 260px) 30%,
      calc(50% + 260px) 100%;
  }

  @media screen and (max-width: 576px) {
    gap: 30px;
    padding: 40px 16px;

    background-position:
      -40% 40%,
      140% 100%;
  }
}

.AuditMethodology_left_container___JJM0 {
  position: -webkit-sticky;
  position: sticky;
  top: 150px;
  height: auto;

  width: 580px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  @media screen and (max-width: 1200px) {
    position: static;

    width: 100%;
    text-align: center;
  }
}

.AuditMethodology_title__43e_q {
  font-weight: 600;
  font-size: 40px;
  line-height: 140%;
}

.AuditMethodology_description__SdRvt>p {
  font-weight: 400;
  font-size: 20px;
  line-height: 160%;
  margin: 0;
}

.AuditMethodology_btn__w5sTw {
  margin-top: 40px;
  width: 165px;
  height: 64px;
  font-size: 20px;

  @media screen and (max-width: 1200px) {
    margin-left: auto;
    margin-right: auto;
  }
}

.AuditMethodology_right_container__9Wd0W {
  position: relative;
  width: 560px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 10;
}

.AuditMethodology_box_container__meodr {
  background-color: #202020;
  padding: 24px;
  display: flex;
  border-radius: 6px;
  flex-direction: column;
  gap: 8px;
}

.AuditMethodology_box_data_title__9qYGl {
  font-weight: 600;
  font-size: 24px;
  line-height: 138%;
}

.AuditMethodology_box_data_description__1Tbtz>p {
  font-weight: 400;
  font-size: 14px;
  margin: 0;
}
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/typography.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
.typography_h1__DecPZ {
  font-size: 78px;

  font-weight: 700;

  @media (max-width: 450px) {
    font-size: 48px;
  }
}

.typography_h2__Dn0zf {
  font-size: 64px;

  @media (max-width: 450px) {
    font-size: 44px;
  }
}

.typography_h3__o3Abb {
  font-size: 52px;

  @media (max-width: 450px) {
    font-size: 40px;
  }
}

.typography_h4__lGrWj {
  font-size: 40px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 28px;
  }
}

.typography_h5__DGJHL {
  font-size: 32px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 22px;
  }
}

.typography_h6__vf_A0 {
  font-size: 24px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 18px;
  }
}

.typography_caption__hfk0A {
  font-size: 12px;
  line-height: 1.67;
  font-weight: normal;
  letter-spacing: 0.01em;
  max-width: 50em;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Heading/Heading.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.Heading_center__XBGsG {
  text-align: center;
}
.Heading_left__ouHog {
  text-align: left;
}
.Heading_right__jsN_Y {
  text-align: right;
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!../../node_modules/react-phone-input-2/lib/style.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
.react-tel-input{font-family:'Roboto',sans-serif;font-size:15px;position:relative;width:100%}.react-tel-input :disabled{cursor:not-allowed}.react-tel-input .flag{width:16px;height:11px;background-image:url(data:image/png;base64,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)}.react-tel-input .ad{background-position:-16px 0}.react-tel-input .ae{background-position:-32px 0}.react-tel-input .af{background-position:-48px 0}.react-tel-input .ag{background-position:-64px 0}.react-tel-input .ai{background-position:-80px 0}.react-tel-input .al{background-position:-96px 0}.react-tel-input .am{background-position:-112px 0}.react-tel-input .ao{background-position:-128px 0}.react-tel-input .ar{background-position:-144px 0}.react-tel-input .as{background-position:-160px 0}.react-tel-input .at{background-position:-176px 0}.react-tel-input .au{background-position:-192px 0}.react-tel-input .aw{background-position:-208px 0}.react-tel-input .az{background-position:-224px 0}.react-tel-input .ba{background-position:-240px 0}.react-tel-input .bb{background-position:0 -11px}.react-tel-input .bd{background-position:-16px -11px}.react-tel-input .be{background-position:-32px -11px}.react-tel-input .bf{background-position:-48px -11px}.react-tel-input .bg{background-position:-64px -11px}.react-tel-input .bh{background-position:-80px -11px}.react-tel-input .bi{background-position:-96px -11px}.react-tel-input .bj{background-position:-112px -11px}.react-tel-input .bm{background-position:-128px -11px}.react-tel-input .bn{background-position:-144px -11px}.react-tel-input .bo{background-position:-160px -11px}.react-tel-input .br{background-position:-176px -11px}.react-tel-input .bs{background-position:-192px -11px}.react-tel-input .bt{background-position:-208px -11px}.react-tel-input .bw{background-position:-224px -11px}.react-tel-input .by{background-position:-240px -11px}.react-tel-input .bz{background-position:0 -22px}.react-tel-input .ca{background-position:-16px -22px}.react-tel-input .cd{background-position:-32px -22px}.react-tel-input .cf{background-position:-48px -22px}.react-tel-input .cg{background-position:-64px -22px}.react-tel-input .ch{background-position:-80px -22px}.react-tel-input .ci{background-position:-96px -22px}.react-tel-input .ck{background-position:-112px -22px}.react-tel-input .cl{background-position:-128px -22px}.react-tel-input .cm{background-position:-144px -22px}.react-tel-input .cn{background-position:-160px -22px}.react-tel-input .co{background-position:-176px -22px}.react-tel-input .cr{background-position:-192px -22px}.react-tel-input .cu{background-position:-208px -22px}.react-tel-input .cv{background-position:-224px -22px}.react-tel-input .cw{background-position:-240px -22px}.react-tel-input .cy{background-position:0 -33px}.react-tel-input .cz{background-position:-16px -33px}.react-tel-input .de{background-position:-32px -33px}.react-tel-input .dj{background-position:-48px -33px}.react-tel-input .dk{background-position:-64px -33px}.react-tel-input .dm{background-position:-80px -33px}.react-tel-input .do{background-position:-96px -33px}.react-tel-input .dz{background-position:-112px -33px}.react-tel-input .ec{background-position:-128px -33px}.react-tel-input .ee{background-position:-144px -33px}.react-tel-input .eg{background-position:-160px -33px}.react-tel-input .er{background-position:-176px -33px}.react-tel-input .es{background-position:-192px -33px}.react-tel-input .et{background-position:-208px -33px}.react-tel-input .fi{background-position:-224px -33px}.react-tel-input .fj{background-position:-240px -33px}.react-tel-input .fk{background-position:0 -44px}.react-tel-input .fm{background-position:-16px -44px}.react-tel-input .fo{background-position:-32px -44px}.react-tel-input .fr,.react-tel-input .bl,.react-tel-input .mf{background-position:-48px -44px}.react-tel-input .ga{background-position:-64px -44px}.react-tel-input .gb{background-position:-80px -44px}.react-tel-input .gd{background-position:-96px -44px}.react-tel-input .ge{background-position:-112px -44px}.react-tel-input .gf{background-position:-128px -44px}.react-tel-input .gh{background-position:-144px -44px}.react-tel-input .gi{background-position:-160px -44px}.react-tel-input .gl{background-position:-176px -44px}.react-tel-input .gm{background-position:-192px -44px}.react-tel-input .gn{background-position:-208px -44px}.react-tel-input .gp{background-position:-224px -44px}.react-tel-input .gq{background-position:-240px -44px}.react-tel-input .gr{background-position:0 -55px}.react-tel-input .gt{background-position:-16px -55px}.react-tel-input .gu{background-position:-32px -55px}.react-tel-input .gw{background-position:-48px -55px}.react-tel-input .gy{background-position:-64px -55px}.react-tel-input .hk{background-position:-80px -55px}.react-tel-input .hn{background-position:-96px -55px}.react-tel-input .hr{background-position:-112px -55px}.react-tel-input .ht{background-position:-128px -55px}.react-tel-input .hu{background-position:-144px -55px}.react-tel-input .id{background-position:-160px -55px}.react-tel-input .ie{background-position:-176px -55px}.react-tel-input .il{background-position:-192px -55px}.react-tel-input .in{background-position:-208px -55px}.react-tel-input .io{background-position:-224px -55px}.react-tel-input .iq{background-position:-240px -55px}.react-tel-input .ir{background-position:0 -66px}.react-tel-input .is{background-position:-16px -66px}.react-tel-input .it{background-position:-32px -66px}.react-tel-input .je{background-position:-144px -154px}.react-tel-input .jm{background-position:-48px -66px}.react-tel-input .jo{background-position:-64px -66px}.react-tel-input .jp{background-position:-80px -66px}.react-tel-input .ke{background-position:-96px -66px}.react-tel-input .kg{background-position:-112px -66px}.react-tel-input .kh{background-position:-128px -66px}.react-tel-input .ki{background-position:-144px -66px}.react-tel-input .xk{background-position:-128px -154px}.react-tel-input .km{background-position:-160px -66px}.react-tel-input .kn{background-position:-176px -66px}.react-tel-input .kp{background-position:-192px -66px}.react-tel-input .kr{background-position:-208px -66px}.react-tel-input .kw{background-position:-224px -66px}.react-tel-input .ky{background-position:-240px -66px}.react-tel-input .kz{background-position:0 -77px}.react-tel-input .la{background-position:-16px -77px}.react-tel-input .lb{background-position:-32px -77px}.react-tel-input .lc{background-position:-48px -77px}.react-tel-input .li{background-position:-64px -77px}.react-tel-input .lk{background-position:-80px -77px}.react-tel-input .lr{background-position:-96px -77px}.react-tel-input .ls{background-position:-112px -77px}.react-tel-input .lt{background-position:-128px -77px}.react-tel-input .lu{background-position:-144px -77px}.react-tel-input .lv{background-position:-160px -77px}.react-tel-input .ly{background-position:-176px -77px}.react-tel-input .ma{background-position:-192px -77px}.react-tel-input .mc{background-position:-208px -77px}.react-tel-input .md{background-position:-224px -77px}.react-tel-input .me{background-position:-112px -154px;height:12px}.react-tel-input .mg{background-position:0 -88px}.react-tel-input .mh{background-position:-16px -88px}.react-tel-input .mk{background-position:-32px -88px}.react-tel-input .ml{background-position:-48px -88px}.react-tel-input .mm{background-position:-64px -88px}.react-tel-input .mn{background-position:-80px -88px}.react-tel-input .mo{background-position:-96px -88px}.react-tel-input .mp{background-position:-112px -88px}.react-tel-input .mq{background-position:-128px -88px}.react-tel-input .mr{background-position:-144px -88px}.react-tel-input .ms{background-position:-160px -88px}.react-tel-input .mt{background-position:-176px -88px}.react-tel-input .mu{background-position:-192px -88px}.react-tel-input .mv{background-position:-208px -88px}.react-tel-input .mw{background-position:-224px -88px}.react-tel-input .mx{background-position:-240px -88px}.react-tel-input .my{background-position:0 -99px}.react-tel-input .mz{background-position:-16px -99px}.react-tel-input .na{background-position:-32px -99px}.react-tel-input .nc{background-position:-48px -99px}.react-tel-input .ne{background-position:-64px -99px}.react-tel-input .nf{background-position:-80px -99px}.react-tel-input .ng{background-position:-96px -99px}.react-tel-input .ni{background-position:-112px -99px}.react-tel-input .nl,.react-tel-input .bq{background-position:-128px -99px}.react-tel-input .no{background-position:-144px -99px}.react-tel-input .np{background-position:-160px -99px}.react-tel-input .nr{background-position:-176px -99px}.react-tel-input .nu{background-position:-192px -99px}.react-tel-input .nz{background-position:-208px -99px}.react-tel-input .om{background-position:-224px -99px}.react-tel-input .pa{background-position:-240px -99px}.react-tel-input .pe{background-position:0 -110px}.react-tel-input .pf{background-position:-16px -110px}.react-tel-input .pg{background-position:-32px -110px}.react-tel-input .ph{background-position:-48px -110px}.react-tel-input .pk{background-position:-64px -110px}.react-tel-input .pl{background-position:-80px -110px}.react-tel-input .pm{background-position:-96px -110px}.react-tel-input .pr{background-position:-112px -110px}.react-tel-input .ps{background-position:-128px -110px}.react-tel-input .pt{background-position:-144px -110px}.react-tel-input .pw{background-position:-160px -110px}.react-tel-input .py{background-position:-176px -110px}.react-tel-input .qa{background-position:-192px -110px}.react-tel-input .re{background-position:-208px -110px}.react-tel-input .ro{background-position:-224px -110px}.react-tel-input .rs{background-position:-240px -110px}.react-tel-input .ru{background-position:0 -121px}.react-tel-input .rw{background-position:-16px -121px}.react-tel-input .sa{background-position:-32px -121px}.react-tel-input .sb{background-position:-48px -121px}.react-tel-input .sc{background-position:-64px -121px}.react-tel-input .sd{background-position:-80px -121px}.react-tel-input .se{background-position:-96px -121px}.react-tel-input .sg{background-position:-112px -121px}.react-tel-input .sh{background-position:-128px -121px}.react-tel-input .si{background-position:-144px -121px}.react-tel-input .sk{background-position:-160px -121px}.react-tel-input .sl{background-position:-176px -121px}.react-tel-input .sm{background-position:-192px -121px}.react-tel-input .sn{background-position:-208px -121px}.react-tel-input .so{background-position:-224px -121px}.react-tel-input .sr{background-position:-240px -121px}.react-tel-input .ss{background-position:0 -132px}.react-tel-input .st{background-position:-16px -132px}.react-tel-input .sv{background-position:-32px -132px}.react-tel-input .sx{background-position:-48px -132px}.react-tel-input .sy{background-position:-64px -132px}.react-tel-input .sz{background-position:-80px -132px}.react-tel-input .tc{background-position:-96px -132px}.react-tel-input .td{background-position:-112px -132px}.react-tel-input .tg{background-position:-128px -132px}.react-tel-input .th{background-position:-144px -132px}.react-tel-input .tj{background-position:-160px -132px}.react-tel-input .tk{background-position:-176px -132px}.react-tel-input .tl{background-position:-192px -132px}.react-tel-input .tm{background-position:-208px -132px}.react-tel-input .tn{background-position:-224px -132px}.react-tel-input .to{background-position:-240px -132px}.react-tel-input .tr{background-position:0 -143px}.react-tel-input .tt{background-position:-16px -143px}.react-tel-input .tv{background-position:-32px -143px}.react-tel-input .tw{background-position:-48px -143px}.react-tel-input .tz{background-position:-64px -143px}.react-tel-input .ua{background-position:-80px -143px}.react-tel-input .ug{background-position:-96px -143px}.react-tel-input .us{background-position:-112px -143px}.react-tel-input .uy{background-position:-128px -143px}.react-tel-input .uz{background-position:-144px -143px}.react-tel-input .va{background-position:-160px -143px}.react-tel-input .vc{background-position:-176px -143px}.react-tel-input .ve{background-position:-192px -143px}.react-tel-input .vg{background-position:-208px -143px}.react-tel-input .vi{background-position:-224px -143px}.react-tel-input .vn{background-position:-240px -143px}.react-tel-input .vu{background-position:0 -154px}.react-tel-input .wf{background-position:-16px -154px}.react-tel-input .ws{background-position:-32px -154px}.react-tel-input .ye{background-position:-48px -154px}.react-tel-input .za{background-position:-64px -154px}.react-tel-input .zm{background-position:-80px -154px}.react-tel-input .zw{background-position:-96px -154px}.react-tel-input *{box-sizing:border-box;-moz-box-sizing:border-box}.react-tel-input .hide{display:none}.react-tel-input .v-hide{visibility:hidden}.react-tel-input .form-control{position:relative;font-size:14px;letter-spacing:.01rem;margin-top:0 !important;margin-bottom:0 !important;padding-left:48px;margin-left:0;background:#FFFFFF;border:1px solid #CACACA;border-radius:5px;line-height:25px;height:35px;width:300px;outline:none}.react-tel-input .form-control.invalid-number{border:1px solid #d79f9f;background-color:#FAF0F0;border-left-color:#cacaca}.react-tel-input .form-control.invalid-number:focus{border:1px solid #d79f9f;border-left-color:#cacaca;background-color:#FAF0F0}.react-tel-input .flag-dropdown{position:absolute;top:0;bottom:0;padding:0;background-color:#f5f5f5;border:1px solid #cacaca;border-radius:3px 0 0 3px}.react-tel-input .flag-dropdown:hover,.react-tel-input .flag-dropdown:focus{cursor:pointer}.react-tel-input .flag-dropdown.invalid-number{border-color:#d79f9f}.react-tel-input .flag-dropdown.open{z-index:2;background:#fff;border-radius:3px 0 0 0}.react-tel-input .flag-dropdown.open .selected-flag{background:#fff;border-radius:3px 0 0 0}.react-tel-input input[disabled]+.flag-dropdown:hover{cursor:default}.react-tel-input input[disabled]+.flag-dropdown:hover .selected-flag{background-color:transparent}.react-tel-input .selected-flag{outline:none;position:relative;width:38px;height:100%;padding:0 0 0 8px;border-radius:3px 0 0 3px}.react-tel-input .selected-flag:hover,.react-tel-input .selected-flag:focus{background-color:#fff}.react-tel-input .selected-flag .flag{position:absolute;top:50%;margin-top:-5px}.react-tel-input .selected-flag .arrow{position:relative;top:50%;margin-top:-2px;left:20px;width:0;height:0;border-left:3px solid transparent;border-right:3px solid transparent;border-top:4px solid #555}.react-tel-input .selected-flag .arrow.up{border-top:none;border-bottom:4px solid #555}.react-tel-input .country-list{outline:none;z-index:1;list-style:none;position:absolute;padding:0;margin:10px 0 10px -1px;box-shadow:1px 2px 10px rgba(0,0,0,0.35);background-color:white;width:300px;max-height:200px;overflow-y:scroll;border-radius:0 0 3px 3px}.react-tel-input .country-list .flag{display:inline-block}.react-tel-input .country-list .divider{padding-bottom:5px;margin-bottom:5px;border-bottom:1px solid #ccc}.react-tel-input .country-list .country{padding:7px 9px}.react-tel-input .country-list .country .dial-code{color:#6b6b6b}.react-tel-input .country-list .country:hover{background-color:#f1f1f1}.react-tel-input .country-list .country.highlight{background-color:#f1f1f1}.react-tel-input .country-list .flag{margin-right:7px;margin-top:2px}.react-tel-input .country-list .country-name{margin-right:6px}.react-tel-input .country-list .search{position:-webkit-sticky;position:sticky;top:0;background-color:#fff;padding:10px 0 6px 10px}.react-tel-input .country-list .search-emoji{font-size:15px}.react-tel-input .country-list .search-box{border:1px solid #cacaca;border-radius:3px;font-size:15px;line-height:15px;margin-left:6px;padding:3px 8px 5px;outline:none}.react-tel-input .country-list .no-entries-message{padding:7px 10px 11px;opacity:.7}.react-tel-input .invalid-number-message{position:absolute;z-index:1;font-size:13px;left:46px;top:-8px;background:#fff;padding:0 2px;color:#de0000}.react-tel-input .special-label{display:none;position:absolute;z-index:1;font-size:13px;left:46px;top:-8px;background:#fff;padding:0 2px;white-space:nowrap}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/ContactUsForm/ContactUsForm.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
.ContactUsForm_ContactUsFormContainer__YKKXj {
  margin: 0;
  padding: 0;
  position: relative;
  color: #FFFFFF;
  background-color: #000000;
}

.ContactUsForm_backgroundImage__R0MDm {
  object-fit: scale-down;
  object-position: bottom right;
}

.ContactUsForm_formWrapper__eiWoS {
  position: relative;
  z-index: 10;
  padding: 5rem 8rem;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: 1024px) {
    padding: 2.5rem 2rem;
  }
}

.ContactUsForm_formHeader__eHuka {
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media (max-width: 576px) {
    text-align: center;
  }
}

.ContactUsForm_formTitle__8l3bx h3 {

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;

  @media (max-width: 576px) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
    letter-spacing: -0.84px;
  }
}

.ContactUsForm_formInstructions__6Uc_U {

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.ContactUsForm_form__OEBsR {
  width: 66.66666666666666666%;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: 768px) {
    width: 100%;
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    width: 85%;
  }

  @media (min-width: 1400px) {
    width: 793px;
  }
}

.ContactUsForm_formFields__b7Hci {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ContactUsForm_personalDetailsWrapper__TMAU1 {
  display: flex;
  gap: 24px;
  flex-direction: column;

  @media (min-width: 576px) and (max-width: 768px) {
    width: 66.6%;
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    width: 66.6%;
  }
}

.ContactUsForm_row__NhA8h {
  display: flex;
  gap: 20px;
}

.ContactUsForm_nameAndInputWrapper__8KgHS {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ContactUsForm_width25__AgX61 {
  width: 23.5%;
}

.ContactUsForm_width47andHalf__qqMnG {
  width: 47.5%;
}

.ContactUsForm_width48andHalf__ueqg_ {
  width: 48.5%;
}

.ContactUsForm_width100__rOL0O {
  width: 100%;
}

.ContactUsForm_firstRow__uLChP {
  flex-direction: row;

  @media (max-width: 1024px) {
    flex-direction: column !important;
    gap: 24px;
  }
}

.ContactUsForm_nameFields__jbH87 {
  @media (max-width: 768px) {
    width: 100%;
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    width: 100%;
  }
}

.ContactUsForm_emailIdField__aTAwq {
  @media (max-width: 768px) {
    width: 100%;
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    width: 100%;
  }
}

.ContactUsForm_secondRow__5_VF3 {
  flex-direction: row;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 24px;
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    flex-direction: column;
    gap: 24px;
  }
}

.ContactUsForm_companyNameWrapper__DbhPp {
  @media (max-width: 1024px) {
    width: 100%;
  }
}

.ContactUsForm_phoneNumberWrapper__5P8Em {
  @media (max-width: 1024px) {
    width: 100%;
  }
}

.ContactUsForm_formLabel__CQ6k7 {

  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;
}

.ContactUsForm_formInput__JTG1G {
  background: #202020;
  color: #FFFFFF;
  border-radius: 3px;
  height: 41px;
  border: none;
  padding: 10px;
}

.ContactUsForm_formInputPhone__mbC7U {
  background: #202020 !important;
  color: #FFFFFF !important;
  height: 41px !important;
  width: 100% !important;
  border: none !important;
  padding: 10px;
  box-shadow: none !important;
}

.ContactUsForm_formInputPhone_dial_icon__ACayh {
  background: #202020 !important;
  color: #FFFFFF !important;
  height: 41px !important;
  padding: 10px;
  border: none !important;
  box-shadow: none !important;
}

.ContactUsForm_ph_number_countries_dropdown__0QqM8 {
  color: #000000 !important;
}

.ContactUsForm_formInputForHowCanWeHelpYou__tBB2E {
  height: 82px;
  max-height: 250px;
}

.ContactUsForm_formInput__JTG1G:focus-visible {
  border: 0;
  margin: 0;
}

.ContactUsForm_consentRow__b2Tqq {
  gap: 12px;
  justify-content: normal;
}

.ContactUsForm_consentText__5t6pW {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: pointer;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.ContactUsForm_submitButtonRow__nzSUd {
  align-items: center;
  justify-content: start;
  gap: 24px;

  @media (max-width: 767px) {
    flex-direction: column;
    gap: 24px;
  }
}

.ContactUsForm_submitButton__bK3PD {
  padding: 16px 36px !important;

  @media (max-width: 767px) {
    width: 100%;
  }
}

.ContactUsForm_submitButton__bK3PD>div {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.ContactUsForm_submitButton__bK3PD::before {
  border-radius: 6px;
  padding: 2px;
}

.ContactUsForm_linkedInButton__s2Bvd {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: none;
}

.ContactUsForm_errorInput__aanZ3 {
  border: 1px solid #ff0000 !important;
}

.ContactUsForm_errorMessages___zK6x {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.ContactUsForm_errorLabel___6So3 {
  color: #ff0000;
}

.ContactUsForm_container_spinner__tPtto {
  position: relative;
  width: 170px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  background-image: linear-gradient(#000000, #000000),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.ContactUsForm_spinner__ttpx2 {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(93deg,
      #febe10 0%,
      #f47a37 30.56%,
      #f05443 53.47%,
      #d91a5f 75.75%,
      #b41f5e 100%);

  -webkit-mask-image: radial-gradient(circle,
      rgba(0, 0, 0, 0) 55%,
      rgba(0, 0, 0, 1) 60%);
  animation: ContactUsForm_spin__Xc8KP 0.8s linear infinite;
}

@keyframes ContactUsForm_spin__Xc8KP {
  to {
    transform: rotate(360deg);
  }
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CTA/Cta.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.Cta_ctaContainer___sfgZ {
  background-color: #000000;
  height: 100%;
  padding: 80px 0px;

  @media (max-width: 769px) {
    padding: 40px 0px;
    height: 100%;
  }

  @media (max-width: 550px) {
    padding: 40px 0px;
    height: 100%;
  }

  @media (max-width: 320px) {
    padding: 20px 0px;
    height: 100%;
  }
}

.Cta_ctaWrapper__TkOMF {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;

  @media (max-width: 769px) {
    flex-direction: column;
    text-align: center;
  }
}

.Cta_ctaHeading___2l6Z {
  display: block;
  width: 872px;

  @media (max-width: 769px) {
    margin-bottom: 24px;
    width: 100%;
  }
}

.Cta_ctaHeading___2l6Z>h2,
.Cta_ctaHeading___2l6Z>h3 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.Cta_btn__Fsqyb {
  min-width: 165px !important;
  height: 64px !important;
  font-size: 20px !important;
}

.Cta_downloadLinkWrapper__dMcXu {
  position: relative;
  text-decoration: none;
  transition: 0.2s linear;
}

.Cta_downloadLinkWrapper__dMcXu::before {
  content: '';
  position: absolute;
  top: 0px;
  left: -1px;
  right: -1px;
  bottom: 0px;
  border-radius: 3px;
  padding: 2.5px;
  /* This is the width of the border */
  background: linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  -webkit-mask:
    linear-gradient(#FFFFFF 0 0) content-box,
    linear-gradient(#FFFFFF 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.Cta_downloadLinkWrapper__dMcXu:hover {
  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.Cta_downloadLink__7uzCi {
  width: 165px;
  height: 64px;
  font-size: 20px;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
  text-align: center;
  align-content: center;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Deliverables/Deliverables.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
.Deliverables_container__FBXgP {
    padding: 80px 124px;
    background-color: #F3F3F3;
    display: flex;
    flex-direction: column;
    gap: 40px;

    @media screen and (max-width: 768px) {
        padding: 40px 32px;
    }

    @media screen and (max-width: 576px) {
        padding: 40px 16px;
    }
}

.Deliverables_content__CSU0C {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.Deliverables_title___JPMn h2 {
    color: #000000;

    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 140%;
    letter-spacing: -0.8px;
    text-align: center;
}

.Deliverables_description__JuxDZ {
    color: #000000;

    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 160%;
    text-align: center;
}

.Deliverables_embla__p3Rlv {
    max-width: 100%;
    margin: auto;
    --slide-height: auto;
    --slide-spacing: 20px;
    --slide-size: auto;
    /* position: relative; */
    /* pointer-events: none; */
}

.Deliverables_embla__viewport__OXMnL {
    overflow: hidden;
}

.Deliverables_embla__container__qlzfy {
    -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
    display: flex;
    touch-action: pan-y pinch-zoom;
    margin-left: calc(var(--slide-spacing) * -1);
}

.Deliverables_embla__slide__IUgVj {
    flex: 0 0 var(--slide-size);
    min-width: 0;
    padding-left: var(--slide-spacing);
    display: flex;
}

.Deliverables_embla__slide__number__FiJJc {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 283px;
    padding: 24px;
    background-color: #FFFFFF;
}

.Deliverables_box_title__e1TZe>h3 {
    color: #000000;

    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 148%;
    text-align: left;
    /* 29.6px */
}

.Deliverables_box_description__D8SCI {
    color: #000000;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.Deliverables_embla__controls__UBL9D {
    display: grid;
    justify-content: center;
    grid-gap: 1.2rem;
    gap: 1.2rem;
    margin-top: 30px;
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/emlaDots.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
.emlaDots_embla__controls__N5QLC {
  display: flex;
  justify-content: center;
  padding-top: 40px;
}

.emlaDots_embla__dots__k0Nk6 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
  outline: none;
  border: 0;

  @media screen and (max-width: 850px) {
    justify-content: center;
  }
}
.emlaDots_embla__dot__pbwQa {
  padding: 0 !important;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  width: 11px;
  height: 11px;
  margin: 0 3.5px;
  border: 0;
  border-radius: 50%;
  outline: none;
  transition: 0.5s width;
}
.emlaDots_embla__dot_bg_white__WQjvs {
  background: #d9d9d9;
}
.emlaDots_embla__dot_selected__ecFy0 {
  width: 26px;
  height: 12px;
  border: 0;
  outline: none;
  border-radius: 20px;
  background: linear-gradient(
    93.12deg,
    #FEBE10 0%,
    #F47A37 30.56%,
    #F05443 53.47%,
    #D91A5F 75.75%,
    #B41F5E 100%
  );
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Faq/Faq.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.Faq_container__UV9bI {
  padding: 5rem 7.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.5rem;
  background-color: #000000;
  color: #FFFFFF;

  @media screen and (max-width: 768px) {
    padding: 5rem 2rem;
  }
}

.Faq_title__R4Xnr>h2 {
  text-align: center;
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 138%;
  }
}

.Faq_faqsWrapper__nvoeK {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 1192px;

  @media screen and (max-width: 1400px) {
    width: 100%;
  }
}

.Faq_accordion__item__EFT6Y {
  border-bottom: 2px solid transparent;
  border-image: linear-gradient(93deg,
      #febe10 0%,
      #f47a37 30.56%,
      #f05443 53.47%,
      #d91a5f 75.75%,
      #b41f5e 100%);
  border-image-slice: 1;
  width: 100%;

  @media screen and (max-width: 576px) {
    padding: 12px 0px;
  }
}

.Faq_accordion__header___9611>button {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 0;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
  letter-spacing: 0.48px;

  @media screen and (max-width: 768px) {
    font-size: 18px;
    letter-spacing: 0.18px;
  }
}

.Faq_accordion__body__tucUq {
  color: #FFFFFF;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  padding-bottom: 20px;
  margin: 0 24px;

  @media screen and (max-width: 768px) {
    font-size: 16px;
    letter-spacing: 0.18px;
  }

  @media screen and (max-width: 576px) {
    padding-bottom: 5px;
  }
}
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/VideoModal/VideoModal.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
.VideoModal_respcontainer__rM1yN {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%;
}

.VideoModal_respiframe__0Aj7P {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.VideoModal_modal-header__klYOg {
  border-bottom: 0;
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Breadcrumb/Breadcrumb.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
.Breadcrumb_breadcrumb__Q0xQA {

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.Breadcrumb_breadcrumb__Q0xQA a {
  color: #FFFFFF;
  text-decoration: none;
}

.Breadcrumb_arrow_style___ICIk {
  color: #FFFFFF;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
.CircularButtonWithArrow_container__9Cvr1 {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.CircularButtonWithArrow_circle__H7jjo {
  position: relative;
  width: 100%;
  height: 100%;
}

.CircularButtonWithArrow_arrow__h3ojH {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease-in-out;
}

.CircularButtonWithArrow_container__9Cvr1:hover .CircularButtonWithArrow_arrow__h3ojH {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.CircularButtonWithArrow_arrowImage__G7E_X {
  width: 100%;
  height: 100%;
}

.CircularButtonWithArrow_arrow_scroll__a_DTi {
  position: absolute;
  top: 33%;
  left: 21%;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/ImageWithBlurPreview/ImageWithBlurPreview.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
.ImageWithBlurPreview_image_hidden__8NnZq {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.ImageWithBlurPreview_image_visible___YMAQ {
  opacity: 1;
}

.ImageWithBlurPreview_mobile_image__1Vbfb {
  object-fit: cover;
  object-position: center center;
  
  @media screen and (max-width: 800px) {
    object-position: center 20%;
  }
}

/* Show desktop images only on desktop screens when mobile image is available */
.ImageWithBlurPreview_desktop_only__JS73q {
  display: block;
  
  @media screen and (max-width: 1023px) {
    display: none !important;
  }
}

/* Show mobile images only on mobile and tablet screens */
.ImageWithBlurPreview_mobile_tablet_only__y4ZJ9 {
  display: none;

  @media screen and (max-width: 1023px) {
    display: block;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/HeroSection/HeroSection.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
.HeroSection_main_container__a_G6M {
  position: relative;
  overflow: hidden;

  /* Ensure proper height for mobile images */
  @media screen and (max-width: 1023px) {
    min-height: 500px;
  }

  @media screen and (max-width: 767px) {
    min-height: 450px;
  }
}

.HeroSection_inner_container__DZ5z1 {
  display: flex;
  flex-direction: column;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 2000px) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.HeroSection_inner_container_resources__1RjK7 {
  display: flex;
  flex-direction: column;
  gap: 74px;
  min-height: 441px;
  max-width: 803px;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 768px) {
    padding: 20px 30px 0 30px;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px;
  }
}

.HeroSection_inner_container_partners__6Ff8X {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 2000px) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.HeroSection_section__nzkUY {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.HeroSection_section_without_breadcrumbs__SJ09S {
  margin-top: 188px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  font-size: 20px;
}

.HeroSection_tag__ZxYj0 {
  color: #FFFFFF;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background-color: #000000;
  padding: 2px 6px;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 3px;
}

.HeroSection_background_image__CGewD {
  background-color: #000000;
  object-fit: cover;
  transition: transform 0.5s ease;
  z-index: -1;

  /* Responsive object-fit for mobile and tablet screens to prevent cutoff */
  @media screen and (max-width: 1023px) {
    object-fit: cover;
    object-position: center top;
  }

  /* For very small mobile screens, ensure full image visibility */
  @media screen and (max-width: 800px) {
    object-fit: fill;
    object-position: center center;
  }
}

.HeroSection_main_container__a_G6M:hover .HeroSection_background_image__CGewD {
  transform: scale(1.1);
}

.HeroSection_title__im1kR > h1 {
  font-size: 78px;
  font-style: normal;
  font-weight: 600;
  line-height: 113%;
  color: #FFFFFF;

  @media screen and (max-width: 768px) {
    font-size: 64px;
    line-height: 76.8px;
    letter-spacing: 1.28px;
  }

  @media screen and (max-width: 450px) {
    font-size: 44px;
    line-height: 118%;
    letter-spacing: -1.76px;
  }
}

.HeroSection_hero_desc__ui48U {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: #FFFFFF;
}

.HeroSection_btn__6f4Xt {
  height: 62px;
  width: 192px;
  font-size: 20px;
  padding: 16px 36px;
}

.HeroSection_inner_container_ai_readiness__FsE8Q {
  max-width: 60%;
  padding: 70px 0 105px 138px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 1440px) {
    padding: 0 0 91px 112px;
  }

  @media screen and (max-width: 1024px) {
    max-width: 100%;
    padding: 24px 20px;
  }

  @media screen and (max-width: 450px) {
    padding: 24px 4px;
  }
}

.HeroSection_heading__jTq5w > h1 {
  font-weight: 700;
  font-size: 48px;
  line-height: 129%;
  color: #FFFFFF;
}

.HeroSection_description__tUvZp {
  font-weight: 400;
  font-size: 22px;
  line-height: 160%;
  color: #FFFFFF;
}

.HeroSection_description__tUvZp > p {
  margin: 0;
}

.HeroSection_cta__drDMW {
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  width: 260px;
  height: 62px;
  padding: 16px 36px;
  border-radius: 6px;
  border-width: 2px;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/YTVideo/YTVideo.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.YTVideo_iframe__7nxX4,
.YTVideo_thumbnail__i7Jo5 {
  background-color: #000000;
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.YTVideo_thumbnail__i7Jo5:hover {
  cursor: pointer;
}

.YTVideo_playButton__eMjS_ {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.YTVideo_playButton__eMjS_:hover {
  cursor: pointer;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/PodcastsSeries/PodcastsSeries.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
.PodcastsSeries_podcast_series_container__oApK6 {
  margin: 0;
  padding: 0;
}

.PodcastsSeries_podcast_series__IKJfU {
  padding: 5rem 9.375rem;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;

  @media (max-width: 1440px) {
    padding: 5rem 7.75rem;
  }

  @media (max-width: 1200px) {
    padding: 5rem 2rem;
  }

  @media (max-width: 576px) {
    padding: 2.5rem 1rem;
  }
}

.PodcastsSeries_podcast_variant_black__W_syP {
  color: #FFFFFF !important;
  background-color: #000000 !important;
}

.PodcastsSeries_podcast_series_title__iQxs_ > h2 {
  text-align: center;
  font-size: 40px;
  font-weight: 600;
  line-height: 140%; /* 56px */
  letter-spacing: -0.8px;
}

.PodcastsSeries_episode_tabs__7kqup {
  width: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;

  @media (max-width: 1024px) {
    padding-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
}

.PodcastsSeries_episode_tabs__7kqup::-webkit-scrollbar {
  height: 5px !important;
}

.PodcastsSeries_episode_tabs__7kqup::-webkit-scrollbar-thumb {
  background-color: #B1B1B1;
  border-radius: 8px;
}

.PodcastsSeries_tab_button_varient_black__mk0K7 {
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #FFFFFF;
  background: #000000;
  color: #FFFFFF;
}

.PodcastsSeries_tab_button_active_varient_black__9QF5d {
  background: #FFFFFF;
  color: #000000;
}

.PodcastsSeries_tab_button_varient_white__QtuIq {
  padding: 6px 10px;
  border-radius: 8px;
  border: 1px solid #000000;
  background: #FFFFFF;
}

.PodcastsSeries_tab_button_active_varient_white__7Fnau {
  background: #000000;
  color: #FFFFFF;
}

.PodcastsSeries_episode_content__tM0_Y {
  display: none;
}

.PodcastsSeries_episode_content_active__Am1lh {
  display: block;
}

.PodcastsSeries_content_wrapper__WBQB8 {
  max-width: 1200px;
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;

  @media (max-width: 1024px) {
    flex-direction: column;
    gap: 20px;
  }
}

.PodcastsSeries_flex_row_reverse__ss7ct {
  flex-direction: row-reverse;

  @media (max-width: 1024px) {
    flex-direction: column;
  }
}

.PodcastsSeries_video_section__unJdY {
  position: relative;
  width: 50%;
  height: 556px;

  @media (max-width: 1024px) {
    max-width: 100%;
    width: 100%;
  }

  @media screen and (max-width: 576px) {
    height: 350px;
  }
}

.PodcastsSeries_video_thumbnail__eIIe6 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.PodcastsSeries_content_section__F4z_C {
  width: 50%;

  @media (max-width: 1024px) {
    width: 100%;
  }
}

.PodcastsSeries_episode_title__JnrRE > h3 {
  font-size: 40px;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
}

.PodcastsSeries_episode_description__mS9nG {
  margin-top: 24px;
  font-size: 20px;
  font-weight: 400;
  line-height: 160%;
}

.PodcastsSeries_audio_section__J_UEg {
  display: flex;
  gap: 12px;
  align-items: center;
}

.PodcastsSeries_audio_player__qKlVF {
  width: 100%;
  height: 50px;
}

.PodcastsSeries_spotify_link__ygiHm {
  font-size: 20px;
  font-weight: 400;
  line-height: 160%;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/SliderButtons/SliderButtons.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
.SliderButtons_embla__button__UqySv {
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0 10px;
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
}
.SliderButtons_embla__button__UqySv:disabled {
  color: gray;
}
/* .embla__button__svg {
  width: 35%;
  height: 35%;
} */

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/ServicesCard/ServicesCard.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
.ServicesCard_ourServiceSection__23gYH {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 0;
  gap: 40px;
}

.ServicesCard_ourServiceContainer__zSeey {
  display: flex;
  justify-content: center;
}

.ServicesCard_title_description_container__Am4xj {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ServicesCard_ourServiceTitle__l5Cpx>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;
  color: #000000;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.ServicesCard_ourServiceSubtitle__OdWaQ {
  color: #000000;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  padding: 0 12px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;

  @media (max-width: 450px) {
    font-size: 16px;
    display: inline-block;
    padding: 0 16px;
  }
}

.ServicesCard_cardContainer__uZF82 {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  transition:
    width 0.8s ease-in-out,
    height 0.8s ease-in-out;

  @media (max-width: 1024px) {
    width: 680px !important;
  }

  @media (min-width: 424px) and (max-width: 700px) {
    width: 400px !important;
  }

  @media (max-width: 427px) {
    width: 300px !important;
  }
}

.ServicesCard_cardCol__q1SAB {
  display: flex !important;
  flex-direction: column;
}

.ServicesCard_row__ohSeR>* {
  @media (min-width: 320px) and (max-width: 427px) {
    padding-left: 0;
    padding-right: 0;
  }
}

.ServicesCard_backdrop__zRg2i {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  transition: background-color 0.5s ease-in-out;

  @media screen and (max-width: 1024px) {
    background: rgba(0, 0, 0, 0.7);
  }
}

.ServicesCard_cardBody__Ho43o {
  color: #FFFFFF;
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 100%;
  transform: translate(0%, -50%);
  -webkit-transform: translate(0%, -50%);
  opacity: 0;
  visibility: hidden;
  width: 307px;
  transition:
    opacity 0.8s ease-in-out,
    visibility 0.8s ease-in-out;

  @media screen and (min-width: 1025px) and (max-width: 1222px) {
    max-width: 252px;
  }

  @media (min-width: 424px) and (max-width: 700px) {
    width: 299px !important;
  }

  @media (max-width: 424px) {
    width: 250px;
  }

  @media screen and (min-width: 701px) and (max-width: 1024px) {
    width: 628px;
  }
}

.ServicesCard_cardTitle__rgXnJ>h3 {
  font-weight: 600;
  font-size: 32px;
  line-height: 42.24px;
  padding-bottom: 8px;
}

.ServicesCard_cardBody__Ho43o.ServicesCard_visible__KtvT3 {
  opacity: 1;
  left: 5%;
  visibility: visible;
}

.ServicesCard_cardDescription__EcnLo {
  padding-top: 8px;
}

.ServicesCard_cardTag__G481g {
  display: inline-block;
  padding-top: 8px;
  font-size: 18px;
  color: #000000;
}

.ServicesCard_link__AiHA3 {
  text-decoration: none;
  display: table-caption;

  @media (max-width: 1366px) {
    display: block;
    margin: 0 auto;
  }
}

.ServicesCard_serviceImage__J_COX {
  width: 100%;
  height: 100%;
  object-fit: cover;

  @media (max-width: 1366px) {
    width: 100%;
  }
}

.ServicesCard_cardContainer__uZF82:hover .ServicesCard_serviceImage__J_COX {
  transform: scale(1.05);
}

.ServicesCard_serviceRow__Jr_q_ {
  display: flex;
  gap: 20px;
  flex-wrap: nowrap;
  max-width: 1192px;

  @media screen and (min-width: 1025px) and (max-width: 1222px) {
    max-width: 900px;
  }

  @media (max-width: 1024px) {
    justify-content: center;
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 30px;
  }
}

/* variant blackSlideCard styles */

.ServicesCard_blackSlideCardWrapper__6ouEF {
  padding: 80px 0px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ServicesCard_whiteSlideCardWrapper__ItlUP {
  background-color: #F3F3F3;
}

.ServicesCard_blackSlideContainer__Vah24 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.ServicesCard_blackSlideTitle__nywPA>h2 {
  color: #000000;
  margin-bottom: 40px;
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;
  letter-spacing: 0.8px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;

  @media screen and (max-width: 576px) {
    margin-bottom: 30px;
    font-size: 28px;
    line-height: 38.64px;
    letter-spacing: 0.84px;
  }
}

.ServicesCard_blackSlideCard__t82uW {
  min-height: 378px;
  width: 283px;
  border: 0;
  border-radius: 6px;
  background-color: #000000;
  position: relative;
  color: white;
  overflow: hidden;
  box-shadow:
    -5px 35px 130px -90px #ae1f5d60,
    -5px 35px 130px -90px #d31a5e60,
    -5px 35px 130px -90px #ed7a3760,
    -5px 35px 130px -90px #f8b81060;

  @media (max-width: 1024px) {
    transform: translateY(0px);
    bottom: 0;
  }
}

.ServicesCard_whiteSlideCard__I1yS3 {
  background-color: #FFFFFF;
}

.ServicesCard_servicePageLink__M7OKg {
  text-decoration: none;
}

.ServicesCard_blackSlideCardTitle__zyp_w>h3 {
  font-size: 20px;
  color: white;
  padding: 24px;
  font-weight: 600;
  z-index: 2;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ServicesCard_whiteSlideCardTitle__ONJ_a>h3 {
  font-size: 20px;
  color: #000000;
  padding: 24px;
  font-weight: 600;
  z-index: 2;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ServicesCard_blackSlideCardDescription__b7DZp {
  position: absolute;
  top: 175px;
  left: 28px;
  right: 28px;
  font-size: 14px;
  transition: transform 0.3s ease-in-out;
  z-index: 2;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  display: -webkit-box;
  -webkit-line-clamp: 9;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: 1024px) {
    transform: translateY(-50px);
    bottom: 0;
  }
}

.ServicesCard_blackSlideCard__t82uW:hover .ServicesCard_blackSlideCardDescription__b7DZp {
  transform: translateY(-50px);
}

.ServicesCard_blackSlideCard__t82uW:hover .ServicesCard_arrow_button__55fpL {
  transform: translateY(0%);
  bottom: 15px;
}

.ServicesCard_whiteSlideCardDescription__GrqVQ {
  position: absolute;
  color: #000000;
  top: 175px;
  left: 28px;
  right: 28px;
  font-size: 14px;
  transition: transform 0.3s ease-in-out;
  z-index: 2;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  display: -webkit-box;
  -webkit-line-clamp: 9;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: 1024px) {
    transform: translateY(-50px);
    bottom: 0;
  }
}

.ServicesCard_blackSlideCard__t82uW:hover .ServicesCard_whiteSlideCardDescription__GrqVQ {
  transform: translateY(-50px);
}

.ServicesCard_arrow_button__55fpL {
  z-index: 3;
  transform: translateY(145%);
  position: absolute;
  left: 28px;
  bottom: 0;
  transition:
    transform 0.4s ease-in-out,
    bottom 0.4s ease-in-out;

  @media (max-width: 1024px) {
    transform: translateY(0%);
    bottom: 15px;
  }
}

.ServicesCard_gradientImage__6wjkT {
  position: absolute;
  left: -100%;
  top: 20%;
  width: 100%;
  height: 100%;
  transition:
    transform 0.6s ease-in-out,
    opacity 0.6s ease-in-out,
    left 0.6s ease-in-out;
  transform-origin: left center;
  transform: rotate(-15deg);
  z-index: 1;

  @media (max-width: 1024px) {
    transform: rotate(0deg);
    left: 0px;
  }
}

.ServicesCard_blackSlideCard__t82uW:hover .ServicesCard_gradientImage__6wjkT {
  transform: rotate(0deg);
  left: 0px;
}

.ServicesCard_embla__9_NJa {
  max-width: 83%;
  margin: auto;
  --slide-height: auto;
  --slide-spacing: 20px;
  --slide-size: auto;
}

.ServicesCard_embla__viewport__DvMRP {
  overflow: hidden;
}

.ServicesCard_embla__container__1Vbbw {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.ServicesCard_withoutCarouselContainer__l_p_F {
  display: flex;
  justify-content: center;
  gap: 30px;

  .ServicesCard_blackSlideCard__t82uW {
    width: 283px;
  }

  @media screen and (max-width: 1024px) {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}

.ServicesCard_embla__slide__e1rUA {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
}

.ServicesCard_embla__controls__fByMh {
  display: grid;
  grid-template-columns: none;
  justify-content: center;
  /* gap: 1.2rem; */
  margin-top: 40px;
  padding-left: 0;
  /* @media (max-width: breakpoint-md-769) {
    justify-content: center;
    grid-template-columns: none;
    padding-left: 0;
  } */
}

.ServicesCard_embla__dots___33hW {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
  outline: none;
  border: 0;
}

.ServicesCard_embla__dot__I2EoK {
  background: rgba(217, 217, 217, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  width: 11px;
  height: 11px;
  margin: 0 3.5px;
  border: 0;
  border-radius: 50%;
  outline: none;
  transition: 0.5s width;
}

.ServicesCard_embla__dot--selected__dTs0A {
  width: 26px;
  height: 12px;
  border: 0;
  outline: none;
  border-radius: 20px;
  background: linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
}

.ServicesCard_centerAlign__L63n2 .ServicesCard_embla__container__1Vbbw {
  justify-content: center;
}

.ServicesCard_headingContainer__vJJ0V {
  display: flex;
  justify-content: space-between;
  padding: 0 12rem;

  @media screen and (max-width: 1500px) {
    padding: 0 6.5rem;
  }

  @media screen and (max-width: 1200px) {
    padding: 0 4rem;

  }


}

.ServicesCard_carouselArrowButtons__PDtEe {
  display: flex;

  @media (max-width: 769px) {
    display: none;
  }
}

.ServicesCard_embla__controls_whiteslide__4CJCH {
  display: grid;
  grid-template-columns: none;
  justify-content: center;
  margin-top: 40px;
  padding-left: 0;
  display: none;

  @media screen and (max-width: 769px) {
    display: grid;

  }
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/TrustedPartners/TrustedPartners.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
.TrustedPartners_heading__TpTyk {
  font-weight: 600;
  font-size: 32px;
  line-height: 42.24px;
  letter-spacing: -0.32px;
  text-align: center;

  @media screen and (max-width: 576px) {
    font-size: 22px;
    line-height: 30.36px;
    letter-spacing: 0;
  }
}

.TrustedPartners_trustedPartnersWrapper__YXGuZ {
  background-color: #000000;
  color: #FFFFFF;
  width: 100%;
  max-height: 200px;
  height: 200px;
  padding-top: 40px;
  padding-bottom: 40px;
}
.TrustedPartners_logoWrapper__srGFz {
  padding-top: 40px;
}
.TrustedPartners_logo__cS0OM {
  margin: 0 12px;
}
.TrustedPartners_embla__KXkkb {
  max-width: 80%;
  margin: auto;
  --slide-height: 5rem;
  --slide-spacing: 10rem;
  --slide-size: 10%;
  position: relative;
  pointer-events: none;
}
.TrustedPartners_embla__KXkkb::before,
.TrustedPartners_embla__KXkkb::after {
  content: '';
  position: absolute;

  bottom: 0;
  height: 60px;
  width: 60px; /* Adjust width as needed */
  background: linear-gradient(to left, transparent, rgba(0, 0, 0, 1));
  z-index: 1;
}

.TrustedPartners_embla__KXkkb::before {
  left: 0;
}

.TrustedPartners_embla__KXkkb::after {
  right: 0;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 1));
}
.TrustedPartners_embla__viewport__oxfi5 {
  overflow: hidden;
}
.TrustedPartners_embla__container__DgEHJ {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.TrustedPartners_embla__slide__GQyPq {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
  display: flex;
  justify-content: center;
}
.TrustedPartners_embla__slide__number__5pa4O {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
}
.TrustedPartners_embla__controls__pF7N4 {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  grid-gap: 1.2rem;
  gap: 1.2rem;
  margin-top: 1.8rem;
}
.TrustedPartners_embla__buttons__q_z4q {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 0.6rem;
  gap: 0.6rem;
  align-items: center;
}
.TrustedPartners_embla__button__0eRDh {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: var(--text-body);
  display: flex;
  align-items: center;
  justify-content: center;
}
.TrustedPartners_embla__button__0eRDh:disabled {
  color: var(--detail-high-contrast);
}
.TrustedPartners_embla__button__svg__HJ4Ed {
  width: 35%;
  height: 35%;
}
.TrustedPartners_embla__play__TmeKo {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: flex-end;
  color: var(--text-body);
  font-weight: 700;
  font-size: 1.4rem;
  padding: 0 2.4rem;
  min-width: 8.4rem;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CaseStudyCard/CaseStudyCard.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
.CaseStudyCard_main_container__96N59 {
  min-height: 756px;
  background: #000000;
  padding: 80px 0;
  display: flex;
  justify-content: center;
  overflow: hidden;

  @media screen and (max-width: 1285px) {
    padding: 80px 32px;
    min-height: 808px;
  }

  @media screen and (max-width: 450px) {
    padding: 40px 32px;
  }
}

.CaseStudyCard_main_container_black__G3pCR {
  background: #FFFFFF;
}

.CaseStudyCard_inner_container__vDN1b {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.CaseStudyCard_heading_box__eeujF {
  display: flex;
  justify-content: space-between;
  max-width: 1192px;

  @media screen and (max-width: 550px) {
    flex-direction: column;
    gap: 24px;
    align-items: center;

    .CaseStudyCard_title__4QdnQ {
      font-size: 28px;
      line-height: 138%;
    }
  }
}

.CaseStudyCard_title__4QdnQ>h2 {
  color: #FFFFFF;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 56px;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 138%;
  }
}

.CaseStudyCard_title_black__ImpW_>h2 {
  color: #000000;
}

.CaseStudyCard_view_all_link__TTQDU {
  display: flex;
  align-items: center;
  color: #FFFFFF;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 25px;
  z-index: 2;
}

.CaseStudyCard_view_all_link_black__D3xdG {
  color: #000000;
  z-index: 2;
}

.CaseStudyCard_card_box__Xjr39 {
  display: flex;
  justify-content: center;
  gap: 20px;
  position: relative;
  max-width: 1192px;
}

.CaseStudyCard_card__OO_RN {
  position: relative;
  display: flex;
  height: 500px;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 6px;
  transition: transform 0.3s ease-in-out;
}

.CaseStudyCard_overlay__4v5VO {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 24px;
  z-index: 1;
}

.CaseStudyCard_badge__rC8iV {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: #FFFFFF;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  width: -moz-fit-content;
  width: fit-content;
}

.CaseStudyCard_box_title__Gef6D h3 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 148%;
  color: #FFFFFF;
}

.CaseStudyCard_arrow_button__eiNpn {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  z-index: 10;
  bottom: -12%;
  right: 24px;
  transition: transform 0.5s ease;
}

.CaseStudyCard_card__OO_RN:hover {
  .CaseStudyCard_arrow_button__eiNpn {
    transform: translateY(-150%);
  }

  @media screen and (min-width: 992px) {
    .CaseStudyCard_image__TD74z {
      filter: blur(8px);
    }
  }

}

.CaseStudyCard_card__OO_RN:not(:hover) {
  .CaseStudyCard_arrow_button__eiNpn {
    transform: translateY(0);
  }
}

@media screen and (max-width: 991px) {
  .CaseStudyCard_card__OO_RN:hover .CaseStudyCard_arrow_button__eiNpn {
    transform: none;
  }

  .CaseStudyCard_arrow_button__eiNpn {
    transition: none;
    bottom: 5%;
    right: 30px;
  }
}

.CaseStudyCard_embla__gOFjR {
  max-width: 68rem;

  /* --slide-size: 12%; */
  @media screen and (max-width: 1222px) {
    max-width: 55rem;
  }

  @media screen and (max-width: 1047px) {
    max-width: 45rem;
  }

  @media screen and (max-width: 869px) {
    max-width: 35rem;
  }

  @media screen and (max-width: 725px) {
    max-width: 25rem;
  }

  @media screen and (max-width: 550px) {
    max-width: 18rem;
  }
}

.CaseStudyCard_embla__viewport__Ul1O_ {
  overflow: hidden;
}

.CaseStudyCard_embla__container__FqZXo {
  display: flex;
}

.CaseStudyCard_embla__slide__cTe1c {
  margin-left: 20px;
}

.CaseStudyCard_carousel__pTtNG {
  padding: 0;
  position: relative;
  display: flex;
  justify-content: center;

  @media screen and (max-width: 400px) {
    padding: 0 10px;
  }

  @media screen and (min-width: 401px) and (max-width: 450px) {
    padding: 0 38px !important;
  }

  .CaseStudyCard_top_left__7E6bt {
    top: -68px;
    left: -32px;
  }

  .CaseStudyCard_bottom_right__wardK {
    bottom: 11px;
    right: -25px;
  }
}

.CaseStudyCard_gradient_image_wrapper__19Rwa {
  position: absolute;
  width: 240px;
  height: 194px;
  z-index: 0;
}

.CaseStudyCard_top_left__7E6bt {
  top: -68px;
  left: -97px;
}

.CaseStudyCard_bottom_right__wardK {
  bottom: -68px;
  right: -97px;
}

.CaseStudyCard_reversed_image__OiNKO {
  transform: scaleX(-1) scaleY(-1);
  z-index: -1;
}

.CaseStudyCard_expertise_delivered__gbteV {
  display: flex;
  gap: 10px;
  max-width: -moz-fit-content;
  max-width: fit-content;
  flex-wrap: wrap;
}

.CaseStudyCard_smallbox__cWAxA {
  display: flex;
  background-color: #ffffffb3;
  justify-content: center;
  width: max-content;
  padding: 2px 6px;
  border-radius: 3px;
  gap: 2px;

  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  width: auto;

  .CaseStudyCard_pipe__dRvYA {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }
}

.CaseStudyCard_smallbox__cWAxA:last-child {
  .CaseStudyCard_pipe__dRvYA {
    display: none;
  }
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/WhyChooseMTL/WhyChooseMTL.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
.WhyChooseMTL_sectionWrapper__fzz3k {
  background-color: #000000;
  padding-bottom: 40px;
  padding-top: 80px;
}

.WhyChooseMTL_whyChooseMTLContentArea__pZfeM {
  width: 100%;
  height: 100%;
  color: white;
}

.WhyChooseMTL_headingArea__6ygBn {
  text-align: center;
  display: block;
  width: 100%;
  position: relative;
  z-index: 6;

  @media (max-width: 565px) {
    padding: 40px 20px;
  }
}

.WhyChooseMTL_headingArea__6ygBn > div > h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.WhyChooseMTL_subHeading__IIQKc {
  font-size: 20px;
  padding-top: 24px;
}

.WhyChooseMTL_pointsWrapper__p3gVK {
  max-width: 1169px;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  flex-wrap: wrap;
  position: relative;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 3;
}

.WhyChooseMTL_pointsWrapper__p3gVK::before {
  content: '';
  width: 730px;
  height: 730px;
  position: absolute;
  z-index: 2;
}

.WhyChooseMTL_pointCard__cKnPL {
  width: 283px;
  height: 192px;
  background-color: #000000;
  position: relative;
  z-index: 5;
  overflow: hidden;
  margin-top: 40px;
}

.WhyChooseMTL_dividerLine__hu3Pc:last-child {
  display: none;
}

.WhyChooseMTL_cardBackground__wP8DQ {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.WhyChooseMTL_pointCardContent__1uqAo {
  padding: 24px;
}

.WhyChooseMTL_pointCardTitle__EifLs > h3 {
  font-size: 18px;
  font-weight: 600;
}

.WhyChooseMTL_pointCardTitle__EifLs > h3 > p {
  margin-bottom: 8px;
}

.WhyChooseMTL_pointCardDescription__m3wyE {
  font-size: 14px;
  font-weight: 400;
  position: inherit;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.WhyChooseMTL_dividerLine__hu3Pc {
  height: 192px;
  width: 1px;
  background: linear-gradient(
    to bottom,
    #000000,
    #000000,
    #FEBE10,
    #F47A37,
    #F05443,
    #D91A5F,
    #B41F5E
  );
  margin: 40px 9px 0px 9px;
}

@media screen and (max-width: 565px) {
  .WhyChooseMTL_pointCard__cKnPL {
    height: auto;
    margin-top: 0;
    background-image: linear-gradient(#000, #000),
      linear-gradient(
        93.12deg,
        #febe10,
        #f47a37 30.56%,
        #f05443 53.47%,
        #d91a5f 75.75%,
        #b41f5e
      );
    background-origin: border-box;
    background-clip: padding-box, border-box;
    border-bottom: 2px solid transparent;
  }

  .WhyChooseMTL_pointCard__cKnPL:last-child {
    border-bottom: 0px solid transparent;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/TechStack/TechStack.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
.TechStack_ourServiceTitle__FBakW > h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 138%;

  @media screen and (max-width: 576px) {
    font-size: 28px;
  }
}

.TechStack_main_container__o_z_3 {
  padding: 80px 124px;
  display: flex;
  flex-direction: column;
  gap: 40px;

  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }
}

.TechStack_inner_container__WRXIB {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.TechStack_scrollContainer__GYYGG {
  display: flex;
  align-items: center;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  cursor: -webkit-grab;
  cursor: grab;
}

.TechStack_scrollContainer__GYYGG:active {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.TechStack_scrollContainer__GYYGG::-webkit-scrollbar {
  height: 5px;
}

.TechStack_scrollContainer__GYYGG::-webkit-scrollbar-track {
  background: transparent;
}

.TechStack_scrollContainer__GYYGG::-webkit-scrollbar-thumb {
  background: linear-gradient(
    93.12deg,
    #FEBE10 0%,
    #F47A37 30.56%,
    #F05443 53.47%,
    #D91A5F 75.75%,
    #B41F5E 100%
  );
  border-radius: 10px;
}

.TechStack_scrollContent__gbtYq {
  display: flex;
  gap: 20px;
  padding-bottom: 20px;
}

.TechStack_scrollWrapper__Rx_hy {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.TechStack_scrollButton__yN4QG {
  padding: 10px;
  margin-bottom: 24px;
}

.TechStack_tech_tabs_wrapper__OnOkf {
  padding-right: 24px;
  border-right: 1px solid #e4e4e4;

  @media screen and (max-width: 450px) {
    display: flex;
  }
}

.TechStack_tech_tabs_wrapper__OnOkf:last-child {
  border-right: none;
}

.TechStack_tech_tabs__xJesy {
  padding: 16px 24px;
  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  border-radius: 6px;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 450px) {
    text-wrap: wrap;
    width: 240px;
  }
}

.TechStack_activeTab__hyB3m {
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(
      93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.TechStack_iconContainer__7D2GX {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(228, 228, 228, 0.3);
  border-radius: 5px;
  text-align: center;
  width: 95px;
  height: 98px;
}

.TechStack_iconImage__Uo24C {
  width: 78px;
  height: 78px;
  border-radius: 10px;
  margin: 10px;
}

.TechStack_iconTitle__v8lck {
  text-align: center;

  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;
}

.TechStack_tabContent__czr_k {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px 100px;
  padding-top: 40px;

  @media screen and (max-width: 550px) {
    gap: 30px;
  }
}

.TechStack_box__5O_Iu {
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: calc(15% - 40px);
  max-width: calc(15% - 40px);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;

  @media screen and (max-width: 550px) {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: calc(50% - 40px);
    max-width: calc(50% - 40px);
  }

  @media screen and (min-width: 2000px) {
    flex-basis: calc(7% - 40px);
    max-width: calc(7% - 40px);
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/AwardsRecognition/AwardsRecognition.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
.AwardsRecognition_main_container__bPZYw {
  background: #000000;
  padding: 80px 124px;

  @media screen and (max-width: 1200px) {
    padding: 80px 60px;
  }

  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }
}

.AwardsRecognition_inner_container__yC_gY {
  display: flex;
  gap: 40px;
  flex-direction: column;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.AwardsRecognition_main_title__Y92M5 h2 {
  color: #FFFFFF;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 56px;
  text-align: center;

  @media screen and (max-width: 450px) {
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    line-height: 138%;
  }
}

.AwardsRecognition_card__veHDB {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.AwardsRecognition_card_box__bFD7V {
  display: flex;
  justify-content: space-between;
}

.AwardsRecognition_image_box__qB2zX {
  width: 182px;
  height: 182px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  border-radius: 6px;
}

.AwardsRecognition_line__oE817 {
  width: 67px;
  height: 4px;
  background: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );

  border-radius: 2px;
  margin-top: 16px;
}

.AwardsRecognition_awards_title__cIJ1S h3 {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: #FFFFFF;
  text-align: center;
  width: 244px;
  padding: 0 7px;
}

.AwardsRecognition_embla__0_ex8 {
  @media screen and (max-width: 400px) {
    padding: 0 33px;
  }

  @media screen and (min-width: 401px) and (max-width: 450px) {
    padding: 0 54px;
  }

  @media screen and (min-width: 550px) and (max-width: 768px) {
    padding: 0 110px;
  }
}

.AwardsRecognition_embla__viewport__pznrd {
  overflow: hidden;
  @media screen and (min-width: 2000px) {
    justify-self: center;
  }
}

.AwardsRecognition_embla__container__FX_lc {
  display: flex;

  @media screen and (min-width: 2000px) {
    justify-content: center;
  }
}

.AwardsRecognition_embla__slide__hXSKA {
  display: flex;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/ClutchReviews/ClutchReviews.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
.ClutchReviews_container__hPusp {
  margin: 0;
  padding: 80px 120px;
  background-color: #f3f3f3;

  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm-550) {
    padding: 40px 0;
  }
}

.ClutchReviews_title__upiGH {
  margin-bottom: 2.5rem;
}

.ClutchReviews_title__upiGH>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.ClutchReviews_embla__viewport__bQFAo {
  overflow: hidden;
}

.ClutchReviews_embla__container__oNpYv {
  display: flex;
}

.ClutchReviews_embla__slide___LSUE {
  margin-left: 20px;
}

.ClutchReviews_embla__controls__BARkD {
  display: grid;
  justify-content: center;
  grid-gap: 1.2rem;
  gap: 1.2rem;
  margin-top: 40px;
}

.ClutchReviews_imageContainer__iBTm_ {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.ClutchReviews_imageWrapper__4RGT2>img {
  width: 350px;
  height: 439px;

  @media (max-width: 1200px) {
    width: 300px;
    height: auto;
  }
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CircularTagline/CircularTagline.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
.CircularTagline_circularTagline__GF_B5 {
  position: relative;
  display: inline-block;
}
.CircularTagline_arrow__IbcIW {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.CircularTagline_taglineImage__79N_V {
  animation: CircularTagline_rotate__dW2fl 10s linear infinite forwards;
}
@keyframes CircularTagline_rotate__dW2fl {
  100% {
    transform: rotate(-360deg);
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Insights/Insights.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
.Insights_insightContainer__OvaCM {
  padding-left: 0px;
  padding-right: 0px;
}

.Insights_insightSection__WwBO3 {
  margin: 80px 0px;
}

.Insights_insightsHeadingArea__Mlwjq {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (min-width: 1441px) {
    margin: 0 150px;
  }

  @media (max-width: 1441px) {
    margin: 0 124px;
  }

  @media (max-width: 1051px) {
    margin: 40px 32px;
  }

  @media (max-width: 769px) {
    margin: 40px 32px;
  }

  @media (max-width: 550px) {
    text-align: center;
    flex-direction: column;
    justify-content: center;
  }
}

.Insights_titleAndSubtitle__Yaeaz h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.Insights_titleAndSubtitle__Yaeaz {
  max-width: 100%;
  width: 50%;
  color: black;

  @media (max-width: 1051px) {
    width: 75%;
  }

  @media (max-width: 550px) {
    width: 100%;
  }
}

.Insights_subtitle__hclO2 {
  margin-top: 24px;
  display: block;
  font-size: 20px;

  @media (max-width: 550px) {
    margin-bottom: 24px;
  }
}

.Insights_viewMoreLink__csoSV {
  padding-top: 24px;
  display: block;
}

.Insights_insightsSlider__paYXa {
  margin: 40px 0px;
  display: flex;
  align-items: center;

  @media (max-width: 1051px) {
    flex-direction: column;
  }
}

.Insights_sliderImage__Qoe8h {
  max-height: 822px;
  position: relative;

  @media (max-width: 1441px) {
    height: 454px;
  }

  @media (max-width: 550px) {
    height: 250px;
    width: 416px;
  }

  @media (min-width: 1442px) {
    height: 822px;
  }
}

.Insights_image__KN5sK {
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: 0px 6px 6px 0px;
  position: relative !important;
  object-fit: cover;

  @media (max-width: 1051px) {
    border-radius: 6px;
  }
}

.Insights_sliderInfo__sgDAy {
  width: 50%;
  padding-left: 80px;
  padding-right: 90px;

  @media (max-width: 1200px) {
    padding-left: 40px;
    padding-right: 40px;
  }

  @media (max-width: 1051px) {
    padding-left: 32px;
    padding-right: 32px;
    width: 100%;
  }
}

.Insights_embla__wqEbk {
  max-width: 50%;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 100%;
  z-index: 2;

  @media (max-width: 1051px) {
    max-width: 92%;
    --slide-spacing: 2rem;
  }
}

.Insights_embla__viewport__bpGNL {
  border: 0;
  overflow-x: hidden;

  @media (max-width: 1051px) {
    border-radius: 6px;
  }
}

.Insights_embla__container__MFhVe {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
  margin-right: calc(var(--slide-spacing) * -1);
}

.Insights_embla__slide__fHlQH {
  flex: 0 0 var(--slide-size);
  min-width: 542px;
  padding-left: var(--slide-spacing);

  @media (max-width: 1051px) {
    min-width: 384px;
  }
}

.Insights_embla_texts__pMmwU {
  max-width: 100%;
  margin: auto;
  --slide-height: 19rem;
  --slide-text-spacing: 2rem;
  --slide-text-size: 100%;
}

.Insights_embla__viewport__texts__XvhIN {
  overflow-x: hidden;
  z-index: 1;
}

.Insights_embla__container__texts__karly {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
}

.Insights_embla__slide__texts__TFYPH h3 {
  font-weight: 600;
  font-size: 32px;
  line-height: 42.24px;

  @media screen and (max-width: 768px) {
    font-size: 40px;
    line-height: 56px;
  }

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.Insights_embla__slide__texts__TFYPH {
  flex: 0 0 var(--slide-text-size);
  min-width: 0;
  padding-left: var(--slide-text-spacingg);

  @media (max-width: 1051px) {
    padding-top: 40px;
  }

  @media (max-width: 550px) {
    text-align: center;
  }
}

.Insights_embla__controls__bp5l2 {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  grid-gap: 1.2rem;
  gap: 1.2rem;
  margin-top: 40px;

  @media (max-width: 769px) {
    justify-content: center;
    grid-template-columns: none;
    padding-left: 0;
  }
}

.Insights_embla__buttons__HhvUO {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/TabChallenges/TabChallenges.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
.TabChallenges_main_container__Yeok_ {
  padding: 80px 124px;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;

  @media screen and (min-width: 800px) and (max-width: 1399px) {
    padding: 80px 5px;
  }

  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: 1082px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: 550px) {
    padding: 40px 16px;
    gap: 30px;
  }
}

.TabChallenges_main_title___LzQS>h2 {

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  /* 56px */
  letter-spacing: -0.8px;

  @media screen and (max-width: 550px) {
    font-size: 28px;
  }
}

.TabChallenges_inner_container__VIBOE {
  display: flex;
  gap: 20px;
  max-width: 1192px;

  @media screen and (max-width: 1082px) {
    flex-direction: column;
  }
}

.TabChallenges_tab_boxes__jz7r7 {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  max-width: 384px;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 1082px) {
    max-width: -moz-fit-content;
    max-width: fit-content;
    justify-content: center;
  }
}

.TabChallenges_single_image__Th1fg {
  position: relative;
  cursor: pointer;
  height: 122px;
  width: 182px;
}

.TabChallenges_single_image_selected__Id4CY {
  position: relative;
  cursor: pointer;
  background: white;
  border-radius: 8px;
  z-index: 1;
  height: 122px;
  width: 182px;
}

.TabChallenges_single_image_selected__Id4CY::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  z-index: -1;
  border-radius: 8px;
  pointer-events: none;
}

.TabChallenges_imageWrapper__GztbE {
  border-radius: 6px;
}

.TabChallenges_titleWrapper__ywxOV {
  position: absolute;
  bottom: 4px;
  padding: 10px;
  min-height: 71px;
}

.TabChallenges_imageTitle__EDND1 {

  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 160%;
}

.TabChallenges_imageTitle_selected__vUMg8 {
  color: #FFFFFF;

  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 160%;
}

.TabChallenges_overlay___leCZ {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  pointer-events: none;
  border-radius: 6px;
}

.TabChallenges_right_container__xP3Yh {
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  max-width: 788px;

  @media screen and (max-width: 560px) {
    padding-top: 20px;
  }
}

.TabChallenges_right_box__DUt0G {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.TabChallenges_right_title__101FW>h3 {

  font-size: 21px;
  font-style: normal;
  font-weight: 600;
  line-height: 128%;
  letter-spacing: 0.42px;
  color: #000000;
}

.TabChallenges_right_richText__dG9HY {

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 144%;
  color: #000000;
}

.TabChallenges_tab_retail_section__6jmlo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;

  margin: 80px 32px;
}

.TabChallenges_retail_title__T5dts>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 140%;
  letter-spacing: 0.8px;

  @media screen and (max-width: 450px) {
    font-size: 28px;
    line-height: 138%;
  }
}

.TabChallenges_retail_description__xa4Uy>h3 {
  font-weight: 400;
  font-size: 20px;
  line-height: 160%;
  text-align: center;

  @media screen and (max-width: 450px) {
    font-size: 18px;
    line-height: 168%;
  }
}

.TabChallenges_embla__viewport__qS7gX {
  overflow: hidden;
}

.TabChallenges_retail_container__7jUTI,
.TabChallenges_embla__container__5bakk {
  max-width: 90vw;
  display: flex;
  gap: 24px;
}

.TabChallenges_tab_retail_box__lITM9 {
  position: relative;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  width: 283px;
  height: 108px;
  padding: 24px;
  border: 2px solid transparent;
  background-color: #F3F3F3;
  border-radius: 8px;
  cursor: pointer;

  font-weight: 600;
  font-size: 20px;
  line-height: 148%;
}

.TabChallenges_tab_retail_box_selected__lxORI {
  color: #FFFFFF;
  background-image: linear-gradient(#000000, #000000),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}


.TabChallenges_container_cloud__4PTzR {
  padding: 80px 32px;
  background-color: #F3F3F3;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;


  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.TabChallenges_content_cloud__qoP9m {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.TabChallenges_title_cloud__06Edn h2 {
  color: #000000;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;
}

.TabChallenges_description_cloud__Y1wA_ {
  color: #000000;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  text-align: center;
}

.TabChallenges_inner_container_cloud__UiKza {
  display: flex;
  max-width: 1192px;
  gap: inherit;

  @media screen and (max-width: 768px) {
    flex-direction: column;
    gap: 30px;
  }

}

.TabChallenges_tab_boxes_cloud__HpCw7 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 350px;
  overflow-y: auto;

  @media screen and (max-width: breakpoint-sm) {
    width: auto;
  }
}

.TabChallenges_tab_boxes_cloud__HpCw7::-webkit-scrollbar {
  width: 3px;
}

.TabChallenges_tab_boxes_cloud__HpCw7::-webkit-scrollbar-thumb {
  background-color: #8C8B8B;
  border-radius: 6px;
}

.TabChallenges_single_box_cloud__2IuJ5 {
  padding: 20px 20px 20px 0;
  border-bottom: 2px solid #8C8B8B;
  cursor: pointer;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: #000000;
}

.TabChallenges_single_box_selected_cloud__1iS4T {
  padding: 20px 0;
  color: #F05443;
  border-bottom: 2px solid #F05443;
  cursor: pointer;

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.TabChallenges_right_container_cloud__F2P91 {
  padding: 20px;
  background-color: #FFFFFF;
  border-radius: 12px;
  max-width: 620px;
  height: -moz-fit-content;
  height: fit-content;
}

.TabChallenges_right_box_cloud__CgAJh {

  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: #000000;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Testimonial/Testimonial.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
.Testimonial_testimonialWrapper__lUNmx {
  background-color: #000000;
  color: #FFFFFF;
  padding: 80px 150px;

  @media (max-width: 1440px) {
    padding: 80px 124px;
  }

  @media screen and (max-width: 768px) {
    padding: 80px 32px;
  }

  @media screen and (max-width: 427px) {
    padding: 40px 16px;
  }

  overflow: hidden;
}

.Testimonial_sliderContainer__NVrJs {
  display: flex;
  position: relative;
}

.Testimonial_slide__JXsAV {
  margin: 0 10px;
  position: relative;
  cursor: pointer;
}

.Testimonial_img__E835_ {
  border-radius: 10px;

  @media (max-width: 326px) {
    width: 300px !important;
  }

  @media (max-width: 390px) {
    width: 350px;
    height: 230px;
  }
}

.Testimonial_clientTestiMonial__8jCUy {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 28px;
  left: 24px;
}

.Testimonial_clientInfo__GAB8E {
  margin-left: 8px;
}

.Testimonial_clientName__WebvK h3 {
  font-weight: 600;
  font-size: 24px;
  line-height: 33.12px;
  letter-spacing: 0.48px;
}

.Testimonial_clientDesignation__gozKK {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.Testimonial_clientDesignation__gozKK > p {
  margin: 0;
  padding: 0;
}

.Testimonial_testimonialTagline__nvMvM {
  padding-top: 10px;

  @media (max-width: 850px) {
    text-align: center;
    padding: 25px 0px;
  }
}

.Testimonial_testimonialSection__DCq10 {
  display: flex;
  justify-content: center;
  gap: 30px;
  align-items: center;

  @media (max-width: 850px) {
    flex-direction: column;
  }
}

.Testimonial_testimonialHeading__5NM9p {
  margin-right: 15px;

  @media (max-width: 769px) {
    margin-right: 0;
  }
}

.Testimonial_title__c1JnV>h2 {
  font-weight: 600;
  font-size: 40px;
  line-height: 56px;

  @media (max-width: 769px) {
    text-align: center;
  }

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 38.64px;
  }
}

.Testimonial_emblaWrapper__WLJvU {
  position: relative;
  z-index: 2;
}

.Testimonial_bottomLeftGradient__PhmeR {
  position: absolute;
  bottom: -20%;
  left: -13%;
  z-index: -1;

  @media (min-width: 2559px) {
    bottom: -20%;
    left: -6%;
  }

  @media (max-width: 769px) {
    bottom: -8%;
    left: -18%;
  }

  @media (max-width: 450px) {
    bottom: 0%;
    left: 0%;
  }

  @media (max-width: 390px) {
    bottom: -10%;
    left: -13%;
  }
}

.Testimonial_topRightGradient__e2zal {
  position: absolute;
  top: -20%;
  right: -13%;
  z-index: -1;

  @media (min-width: 2559px) {
    bottom: -20%;
    right: -6%;
  }

  @media (max-width: 450px) {
    bottom: -20%;
    right: 0%;
  }

  @media (max-width: 390px) {
    top: -20%;
    right: -13%;
  }
}

.Testimonial_embla__EVnWd {
  --slide-height: 19rem;
  /* --slide-spacing: 1rem; */
  /* --slide-size: 44%; */

  @media (max-width: 326px) {
    width: 300px !important;
    --slide-size: 0.5% !important;
  }

  @media (max-width: 390px) {
    width: 400px !important;
    --slide-size: 87.5% !important;
  }

  @media screen and (max-width: 1243px) {
    width: 657px;
    --slide-size: 60%;
  }

  @media screen and (min-width: 1244px) and (max-width: 2100px) {
    width: 875px;
    --slide-size: 44%;
  }

  @media screen and (min-width: 2100px) and (max-width: 2420px) {
    width: 1451px;
    --slide-size: 27%;
  }
}

.Testimonial_embla__viewport__2MxwZ {
  overflow: hidden;
}

.Testimonial_embla__container__PDox4 {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  /* margin-left: calc(var(--slide-spacing) * -1); */
}

.Testimonial_embla__slide__uHeep {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  /* padding-left: var(--slide-spacing); */

  @media (max-width: 326px) {
    flex: none;
  }
}

.Testimonial_embla__slide__number__ycBDX {
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
}

.Testimonial_embla__controls__E744_ {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  grid-gap: 1.2rem;
  gap: 1.2rem;
  margin-top: 40px;
}

.Testimonial_mobileDots__R1Hex {
  /* display: none;
  @media (max-width: breakpoint-md-850) {
    display: block;
  } */
  display: block;
}

.Testimonial_desktopDots__CGll2 {
  display: none;

  @media (min-width: 1024px) {
    display: block;
  }

  @media (max-width: 768px) {
    display: none;
  }
}
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/TitleDescription/TitleDescription.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
.TitleDescription_container__NABf8 {
  padding: 80px 124px;
  background-color: #000000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;

  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: 576px) {
    padding: 40px 16px;
  }
}

.TitleDescription_employee_container__r7dOC {
  padding: 80px 8px;
  background-color: #000000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;

  @media screen and (max-width: 768px) {
    padding: 40px 32px;
  }

  @media screen and (max-width: 576px) {
    padding: 40px 8px;
  }
}

.TitleDescription_title___rCHZ h2 {
  color: #FFFFFF;

  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -0.8px;
  text-align: center;

  @media screen and (max-width: 576px) {
    font-size: 28px;
    line-height: 138%;
    letter-spacing: -0.8px;
  }
}

.TitleDescription_description__yZGkR {
  color: #FFFFFF;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.TitleDescription_image__wpkOn {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
}


.TitleDescription_employeeGrid__lZEuT {
  display: flex;
  max-width: 1197px;
  flex-wrap: wrap;
  gap: 5px;

  @media screen and (min-width: 990px) and (max-width: 1216px) {
    max-width: 947px;
    justify-content: center;
  }

  @media screen and (min-width: 768px) and (max-width: 990px) {
    max-width: 709px;
    justify-content: center;
  }

  @media screen and (max-width: 768px) {
    max-width: 471px;
    justify-content: center;
  }

  @media screen and (max-width: 576px) {
    max-width: 305px;
    justify-content: center;

  }

}

.TitleDescription_employeeImage__YS6cV {
  @media screen and (max-width: 576px) {
    width: 150px;
    height: 120px;
  }
}

.TitleDescription_img_text_container__xLOG_ {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
}

.TitleDescription_employeeTextContainer__C0ACp {
  display: flex;
  flex-direction: column;
  padding: 20px 0 0 20px;
  width: 295px;
  background-color: #F8F8F8;

  @media screen and (max-width: 576px) {
    padding: 5px;
    width: 150px;
  }
}

.TitleDescription_employeeTitle__1tPzp>h6 {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 140%;
  color: #000000;
}

.TitleDescription_employeeDescription__iY4xW {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: #000000;

}
